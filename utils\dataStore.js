const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const DATA_DIR = path.join(__dirname, '..', 'data');

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Initialize default files if they don't exist
const initializeDataFiles = () => {
  const defaultFiles = [
    { name: 'users.json', default: [] },
    { name: 'tokens.json', default: [] },
    { name: 'messages.json', default: [] },
    { name: 'activation_codes.json', default: [] }
  ];

  defaultFiles.forEach(file => {
    const filePath = path.join(DATA_DIR, file.name);
    if (!fs.existsSync(filePath)) {
      fs.writeJsonSync(filePath, file.default);
    }
  });
};

// Initialize data files
initializeDataFiles();

class DataStore {
  // Generic methods for data manipulation
  static async readData(fileName) {
    try {
      const filePath = path.join(DATA_DIR, fileName);
      if (!fs.existsSync(filePath)) {
        return [];
      }
      return fs.readJsonSync(filePath);
    } catch (error) {
      console.error(`Error reading ${fileName}:`, error);
      return [];
    }
  }

  static async writeData(fileName, data) {
    try {
      const filePath = path.join(DATA_DIR, fileName);
      await fs.writeJson(filePath, data, { spaces: 2 });
      return true;
    } catch (error) {
      console.error(`Error writing to ${fileName}:`, error);
      return false;
    }
  }

  // User-specific methods
  static async getUsers() {
    return this.readData('users.json');
  }

  static async getUserById(userId) {
    const users = await this.getUsers();
    return users.find(user => user.id === userId);
  }

  static async getUserByUsername(username) {
    const users = await this.getUsers();
    return users.find(user => user.username === username);
  }

  static async createUser(userData) {
    const users = await this.getUsers();
    const newUser = {
      id: uuidv4(),
      ...userData,
      messageCount: 0,
      freeMessagesRemaining: 50,
      activationCode: null,
      activationExpiry: null,
      createdAt: new Date().toISOString()
    };
    users.push(newUser);
    await this.writeData('users.json', users);
    return newUser;
  }

  static async updateUser(userId, updates) {
    const users = await this.getUsers();
    const index = users.findIndex(user => user.id === userId);
    if (index !== -1) {
      users[index] = { ...users[index], ...updates };
      await this.writeData('users.json', users);
      return users[index];
    }
    return null;
  }

  // Product-specific methods
  static async getProducts(userId) {
    return this.readData(`products_${userId}.json`);
  }

  static async addProduct(userId, productData) {
    const products = await this.getProducts(userId) || [];
    const newProduct = {
      id: uuidv4(),
      ...productData,
      createdAt: new Date().toISOString()
    };
    products.push(newProduct);
    await this.writeData(`products_${userId}.json`, products);
    return newProduct;
  }

  static async updateProduct(userId, productId, updates) {
    const products = await this.getProducts(userId);
    const index = products.findIndex(product => product.id === productId);
    if (index !== -1) {
      products[index] = { ...products[index], ...updates };
      await this.writeData(`products_${userId}.json`, products);
      return products[index];
    }
    return null;
  }

  static async deleteProduct(userId, productId) {
    const products = await this.getProducts(userId);
    const filteredProducts = products.filter(product => product.id !== productId);
    await this.writeData(`products_${userId}.json`, filteredProducts);
    return true;
  }

  // Message-specific methods
  static async getMessages(userId) {
    const allMessages = await this.readData('messages.json');
    return allMessages.filter(message => message.userId === userId);
  }

  static async getRecentMessages(userId, count = 5) {
    const allMessages = await this.readData('messages.json');
    const userMessages = allMessages.filter(message => message.userId === userId);

    // Sort messages by timestamp (newest first)
    userMessages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Return the most recent messages (limited by count)
    return userMessages.slice(0, count);
  }

  static async getConversationHistory(userId, count = 5) {
    const allMessages = await this.readData('messages.json');
    const userMessages = allMessages.filter(message => message.userId === userId);

    // Sort messages by timestamp (oldest first)
    userMessages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Get the most recent messages
    const recentMessages = userMessages.slice(-count * 2); // Get twice the count to ensure we have pairs

    return recentMessages;
  }

  static async addMessage(messageData) {
    const messages = await this.readData('messages.json');
    const newMessage = {
      id: uuidv4(),
      ...messageData,
      timestamp: new Date().toISOString()
    };
    messages.push(newMessage);
    await this.writeData('messages.json', messages);
    return newMessage;
  }

  // Store information methods
  static async getStoreInfo(userId) {
    try {
      const filePath = path.join(DATA_DIR, `store_info_${userId}.json`);
      if (!fs.existsSync(filePath)) {
        // Create default store info
        const defaultStoreInfo = {
          name: '',
          address: '',
          description: '',
          updatedAt: new Date().toISOString()
        };
        await fs.writeJson(filePath, defaultStoreInfo, { spaces: 2 });
        return defaultStoreInfo;
      }
      return fs.readJsonSync(filePath);
    } catch (error) {
      console.error(`Error reading store info for user ${userId}:`, error);
      return {
        name: '',
        address: '',
        description: '',
        updatedAt: new Date().toISOString()
      };
    }
  }

  static async updateStoreInfo(userId, storeInfo) {
    try {
      const filePath = path.join(DATA_DIR, `store_info_${userId}.json`);
      const currentInfo = await this.getStoreInfo(userId);

      const updatedInfo = {
        ...currentInfo,
        ...storeInfo,
        updatedAt: new Date().toISOString()
      };

      await fs.writeJson(filePath, updatedInfo, { spaces: 2 });
      return updatedInfo;
    } catch (error) {
      console.error(`Error updating store info for user ${userId}:`, error);
      throw error;
    }
  }

  // Orders methods
  static async getOrders(userId) {
    try {
      const filePath = path.join(DATA_DIR, `orders_${userId}.json`);
      if (!fs.existsSync(filePath)) {
        await fs.writeJson(filePath, [], { spaces: 2 });
        return [];
      }
      return fs.readJsonSync(filePath);
    } catch (error) {
      console.error(`Error reading orders for user ${userId}:`, error);
      return [];
    }
  }

  static async addOrder(userId, orderData) {
    try {
      const orders = await this.getOrders(userId);
      const newOrder = {
        id: uuidv4(),
        ...orderData,
        status: orderData.status || 'pending',
        createdAt: new Date().toISOString()
      };
      orders.push(newOrder);

      const filePath = path.join(DATA_DIR, `orders_${userId}.json`);
      await fs.writeJson(filePath, orders, { spaces: 2 });
      return newOrder;
    } catch (error) {
      console.error(`Error adding order for user ${userId}:`, error);
      throw error;
    }
  }

  static async updateOrderStatus(userId, orderId, status) {
    try {
      const orders = await this.getOrders(userId);
      const orderIndex = orders.findIndex(order => order.id === orderId);

      if (orderIndex === -1) {
        throw new Error('Order not found');
      }

      orders[orderIndex].status = status;
      orders[orderIndex].updatedAt = new Date().toISOString();

      const filePath = path.join(DATA_DIR, `orders_${userId}.json`);
      await fs.writeJson(filePath, orders, { spaces: 2 });
      return orders[orderIndex];
    } catch (error) {
      console.error(`Error updating order status for user ${userId}:`, error);
      throw error;
    }
  }

  static async deleteOrder(userId, orderId) {
    try {
      const orders = await this.getOrders(userId);
      const updatedOrders = orders.filter(order => order.id !== orderId);

      if (updatedOrders.length === orders.length) {
        throw new Error('Order not found');
      }

      const filePath = path.join(DATA_DIR, `orders_${userId}.json`);
      await fs.writeJson(filePath, updatedOrders, { spaces: 2 });
      return { success: true };
    } catch (error) {
      console.error(`Error deleting order for user ${userId}:`, error);
      throw error;
    }
  }

  // Activation code methods
  static async getActivationCodes() {
    return this.readData('activation_codes.json');
  }

  static async createActivationCode(codeData) {
    const codes = await this.getActivationCodes();
    const newCode = {
      code: uuidv4().substring(0, 8).toUpperCase(),
      ...codeData,
      used: false,
      createdAt: new Date().toISOString()
    };
    codes.push(newCode);
    await this.writeData('activation_codes.json', codes);
    return newCode;
  }

  static async useActivationCode(code, userId) {
    const codes = await this.getActivationCodes();
    const index = codes.findIndex(c => c.code === code && !c.used);

    if (index !== -1) {
      codes[index].used = true;
      codes[index].usedBy = userId;
      codes[index].usedAt = new Date().toISOString();
      await this.writeData('activation_codes.json', codes);
      return codes[index];
    }
    return null;
  }

  static async validateActivationCode(code) {
    const codes = await this.getActivationCodes();
    return codes.find(c => c.code === code && !c.used);
  }
}

module.exports = DataStore;
