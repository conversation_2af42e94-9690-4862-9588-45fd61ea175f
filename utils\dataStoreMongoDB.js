const { v4: uuidv4 } = require('uuid');

// Import MongoDB models
const User = require('../models/User');
const Product = require('../models/Product');
const Message = require('../models/Message');
const Order = require('../models/Order');
const StoreInfo = require('../models/StoreInfo');
const ActivationCode = require('../models/ActivationCode');

class DataStore {
  // User-specific methods
  static async getUsers() {
    try {
      const users = await User.find({}).lean();
      return users.map(user => ({
        ...user,
        _id: undefined, // Remove MongoDB _id field
        __v: undefined  // Remove MongoDB version field
      }));
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }

  static async getUserById(userId) {
    try {
      const user = await User.findOne({ id: userId }).lean();
      if (user) {
        delete user._id;
        delete user.__v;
      }
      return user;
    } catch (error) {
      console.error(`Error getting user by ID ${userId}:`, error);
      return null;
    }
  }

  static async getUserByUsername(username) {
    try {
      const user = await User.findOne({ username }).lean();
      if (user) {
        delete user._id;
        delete user.__v;
      }
      return user;
    } catch (error) {
      console.error(`Error getting user by username ${username}:`, error);
      return null;
    }
  }

  static async createUser(userData) {
    try {
      const newUser = new User({
        id: uuidv4(),
        ...userData,
        messageCount: 0,
        freeMessagesRemaining: 50,
        activationCode: null,
        activationExpiry: null,
        createdAt: new Date().toISOString()
      });

      const savedUser = await newUser.save();
      const userObj = savedUser.toObject();
      delete userObj._id;
      delete userObj.__v;
      return userObj;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  static async updateUser(userId, updates) {
    try {
      const updatedUser = await User.findOneAndUpdate(
        { id: userId },
        { $set: updates },
        { new: true, lean: true }
      );

      if (updatedUser) {
        delete updatedUser._id;
        delete updatedUser.__v;
      }
      return updatedUser;
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error);
      return null;
    }
  }

  // Product-specific methods
  static async getProducts(userId) {
    try {
      const products = await Product.find({ userId }).lean();
      return products.map(product => ({
        ...product,
        _id: undefined,
        __v: undefined,
        userId: undefined // Remove userId from response to match original API
      }));
    } catch (error) {
      console.error(`Error getting products for user ${userId}:`, error);
      return [];
    }
  }

  static async addProduct(userId, productData) {
    try {
      const newProduct = new Product({
        id: uuidv4(),
        userId,
        ...productData,
        createdAt: new Date().toISOString()
      });

      const savedProduct = await newProduct.save();
      const productObj = savedProduct.toObject();
      delete productObj._id;
      delete productObj.__v;
      delete productObj.userId; // Remove userId from response to match original API
      return productObj;
    } catch (error) {
      console.error(`Error adding product for user ${userId}:`, error);
      throw error;
    }
  }

  static async updateProduct(userId, productId, updates) {
    try {
      const updatedProduct = await Product.findOneAndUpdate(
        { id: productId, userId },
        { $set: updates },
        { new: true, lean: true }
      );

      if (updatedProduct) {
        delete updatedProduct._id;
        delete updatedProduct.__v;
        delete updatedProduct.userId; // Remove userId from response to match original API
      }
      return updatedProduct;
    } catch (error) {
      console.error(`Error updating product ${productId} for user ${userId}:`, error);
      return null;
    }
  }

  static async deleteProduct(userId, productId) {
    try {
      const result = await Product.deleteOne({ id: productId, userId });
      return result.deletedCount > 0;
    } catch (error) {
      console.error(`Error deleting product ${productId} for user ${userId}:`, error);
      return false;
    }
  }

  // Message-specific methods
  static async getMessages(userId) {
    try {
      const messages = await Message.find({ userId }).sort({ timestamp: 1 }).lean();
      return messages.map(message => ({
        ...message,
        _id: undefined,
        __v: undefined,
        userId: undefined // Remove userId from response to match original API
      }));
    } catch (error) {
      console.error(`Error getting messages for user ${userId}:`, error);
      return [];
    }
  }

  static async getConversationHistory(userId, count = 5) {
    try {
      const messages = await Message.find({ userId })
        .sort({ timestamp: 1 })
        .limit(count * 2)
        .lean();

      return messages.map(message => ({
        ...message,
        _id: undefined,
        __v: undefined
      }));
    } catch (error) {
      console.error(`Error getting conversation history for user ${userId}:`, error);
      return [];
    }
  }

  static async addMessage(messageData) {
    try {
      const newMessage = new Message({
        id: uuidv4(),
        ...messageData,
        timestamp: new Date().toISOString()
      });

      const savedMessage = await newMessage.save();
      const messageObj = savedMessage.toObject();
      delete messageObj._id;
      delete messageObj.__v;
      return messageObj;
    } catch (error) {
      console.error('Error adding message:', error);
      throw error;
    }
  }

  // Store information methods
  static async getStoreInfo(userId) {
    try {
      let storeInfo = await StoreInfo.findOne({ userId }).lean();
      
      if (!storeInfo) {
        // Create default store info if it doesn't exist
        const defaultStoreInfo = {
          userId,
          name: '',
          address: '',
          description: '',
          updatedAt: new Date().toISOString()
        };
        
        const newStoreInfo = new StoreInfo(defaultStoreInfo);
        const savedStoreInfo = await newStoreInfo.save();
        storeInfo = savedStoreInfo.toObject();
      }

      if (storeInfo) {
        delete storeInfo._id;
        delete storeInfo.__v;
        delete storeInfo.userId; // Remove userId from response to match original API
      }

      return storeInfo || {
        name: '',
        address: '',
        description: '',
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error getting store info for user ${userId}:`, error);
      return {
        name: '',
        address: '',
        description: '',
        updatedAt: new Date().toISOString()
      };
    }
  }

  static async updateStoreInfo(userId, storeInfo) {
    try {
      const updatedInfo = {
        ...storeInfo,
        updatedAt: new Date().toISOString()
      };

      const result = await StoreInfo.findOneAndUpdate(
        { userId },
        { $set: updatedInfo },
        { new: true, upsert: true, lean: true }
      );

      if (result) {
        delete result._id;
        delete result.__v;
        delete result.userId; // Remove userId from response to match original API
      }

      return result;
    } catch (error) {
      console.error(`Error updating store info for user ${userId}:`, error);
      throw error;
    }
  }

  // Orders methods
  static async getOrders(userId) {
    try {
      const orders = await Order.find({ userId }).sort({ createdAt: -1 }).lean();
      return orders.map(order => ({
        ...order,
        _id: undefined,
        __v: undefined,
        userId: undefined // Remove userId from response to match original API
      }));
    } catch (error) {
      console.error(`Error getting orders for user ${userId}:`, error);
      return [];
    }
  }

  static async addOrder(userId, orderData) {
    try {
      const newOrder = new Order({
        id: uuidv4(),
        userId,
        ...orderData,
        status: orderData.status || 'pending',
        createdAt: new Date().toISOString()
      });

      const savedOrder = await newOrder.save();
      const orderObj = savedOrder.toObject();
      delete orderObj._id;
      delete orderObj.__v;
      delete orderObj.userId; // Remove userId from response to match original API
      return orderObj;
    } catch (error) {
      console.error(`Error adding order for user ${userId}:`, error);
      throw error;
    }
  }

  static async updateOrderStatus(userId, orderId, status) {
    try {
      const updatedOrder = await Order.findOneAndUpdate(
        { id: orderId, userId },
        { $set: { status } },
        { new: true, lean: true }
      );

      if (!updatedOrder) {
        throw new Error('Order not found');
      }

      delete updatedOrder._id;
      delete updatedOrder.__v;
      delete updatedOrder.userId; // Remove userId from response to match original API
      return updatedOrder;
    } catch (error) {
      console.error(`Error updating order status for order ${orderId}:`, error);
      throw error;
    }
  }

  static async deleteOrder(userId, orderId) {
    try {
      const result = await Order.deleteOne({ id: orderId, userId });

      if (result.deletedCount === 0) {
        throw new Error('Order not found');
      }

      return { success: true };
    } catch (error) {
      console.error(`Error deleting order ${orderId} for user ${userId}:`, error);
      throw error;
    }
  }

  // Activation code methods
  static async getActivationCodes() {
    try {
      const codes = await ActivationCode.find({}).lean();
      return codes.map(code => ({
        ...code,
        _id: undefined,
        __v: undefined
      }));
    } catch (error) {
      console.error('Error getting activation codes:', error);
      return [];
    }
  }

  static async createActivationCode(codeData) {
    try {
      const newCode = new ActivationCode({
        code: uuidv4().substring(0, 8).toUpperCase(),
        ...codeData,
        used: false,
        createdAt: new Date().toISOString()
      });

      const savedCode = await newCode.save();
      const codeObj = savedCode.toObject();
      delete codeObj._id;
      delete codeObj.__v;
      return codeObj;
    } catch (error) {
      console.error('Error creating activation code:', error);
      throw error;
    }
  }

  static async useActivationCode(code, userId) {
    try {
      const updatedCode = await ActivationCode.findOneAndUpdate(
        { code, used: false },
        {
          $set: {
            used: true,
            usedBy: userId,
            usedAt: new Date().toISOString()
          }
        },
        { new: true, lean: true }
      );

      if (updatedCode) {
        delete updatedCode._id;
        delete updatedCode.__v;
      }

      return updatedCode;
    } catch (error) {
      console.error(`Error using activation code ${code}:`, error);
      return null;
    }
  }

  static async validateActivationCode(code) {
    try {
      const activationCode = await ActivationCode.findOne({ code, used: false }).lean();
      if (activationCode) {
        delete activationCode._id;
        delete activationCode.__v;
      }
      return activationCode;
    } catch (error) {
      console.error(`Error validating activation code ${code}:`, error);
      return null;
    }
  }

  // Legacy compatibility methods (for backward compatibility with JSON file system)
  static async readData(fileName) {
    console.warn(`readData(${fileName}) is deprecated. Use specific methods instead.`);
    return [];
  }

  static async writeData(fileName, data) {
    console.warn(`writeData(${fileName}) is deprecated. Use specific methods instead.`);
    return true;
  }
}

module.exports = DataStore;
