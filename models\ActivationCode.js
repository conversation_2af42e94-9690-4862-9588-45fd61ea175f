const mongoose = require('mongoose');

const activationCodeSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  type: {
    type: String,
    enum: ['full', 'temp'],
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  used: {
    type: Boolean,
    default: false
  },
  usedBy: {
    type: String,
    default: null,
    ref: 'User'
  },
  usedAt: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for better performance
activationCodeSchema.index({ used: 1 });
activationCodeSchema.index({ type: 1 });

module.exports = mongoose.model('ActivationCode', activationCodeSchema);
