{"name": "social-media-automation", "version": "1.0.0", "description": "Automated Instagram/Facebook responses using Node.js, Express, and Google Gemini", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test:db": "node test-database.js", "verify:mongodb": "node verify-mongodb-integration.js", "health:check": "node health-check.js", "migrate:data": "node migrate-data.js", "cleanup:old": "node cleanup-old-files.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.15.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}