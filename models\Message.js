const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: String,
    required: true,
    ref: 'User'
  },
  platform: {
    type: String,
    required: true,
    default: 'test'
  },
  content: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['user', 'ai'],
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for better performance
messageSchema.index({ userId: 1 });
messageSchema.index({ userId: 1, timestamp: -1 });
messageSchema.index({ platform: 1 });

module.exports = mongoose.model('Message', messageSchema);
