const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  username: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  password: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  messageCount: {
    type: Number,
    default: 0
  },
  freeMessagesRemaining: {
    type: Number,
    default: 50
  },
  activationCode: {
    type: String,
    default: null
  },
  activationExpiry: {
    type: Date,
    default: null
  },
  activationType: {
    type: String,
    enum: ['temp', 'full'],
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create additional indexes for better performance
userSchema.index({ email: 1 });

module.exports = mongoose.model('User', userSchema);
