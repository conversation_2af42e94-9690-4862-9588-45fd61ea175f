require('dotenv').config();
const fs = require('fs-extra');
const path = require('path');
const database = require('./utils/database');
const DataStore = require('./utils/dataStoreMongoDB');

// Import models directly for bulk operations
const User = require('./models/User');
const Product = require('./models/Product');
const Message = require('./models/Message');
const Order = require('./models/Order');
const StoreInfo = require('./models/StoreInfo');
const ActivationCode = require('./models/ActivationCode');

const DATA_DIR = path.join(__dirname, 'data');

async function migrateData() {
  console.log('🔄 Starting data migration from JSON files to MongoDB...\n');

  try {
    // Connect to database
    await database.connect();
    console.log('✅ Connected to MongoDB\n');

    // Check if data directory exists
    if (!fs.existsSync(DATA_DIR)) {
      console.log('⚠️ No data directory found. Nothing to migrate.');
      return;
    }

    let totalMigrated = 0;

    // 1. Migrate Users
    console.log('1️⃣ Migrating Users...');
    const usersFile = path.join(DATA_DIR, 'users.json');
    if (fs.existsSync(usersFile)) {
      const users = fs.readJsonSync(usersFile);
      console.log(`Found ${users.length} users to migrate`);
      
      for (const user of users) {
        try {
          // Check if user already exists
          const existingUser = await User.findOne({ id: user.id });
          if (!existingUser) {
            await User.create(user);
            console.log(`✅ Migrated user: ${user.username}`);
            totalMigrated++;
          } else {
            console.log(`⚠️ User already exists: ${user.username}`);
          }
        } catch (error) {
          console.error(`❌ Error migrating user ${user.username}:`, error.message);
        }
      }
    } else {
      console.log('No users.json file found');
    }

    // 2. Migrate Messages
    console.log('\n2️⃣ Migrating Messages...');
    const messagesFile = path.join(DATA_DIR, 'messages.json');
    if (fs.existsSync(messagesFile)) {
      const messages = fs.readJsonSync(messagesFile);
      console.log(`Found ${messages.length} messages to migrate`);
      
      for (const message of messages) {
        try {
          const existingMessage = await Message.findOne({ id: message.id });
          if (!existingMessage) {
            await Message.create(message);
            totalMigrated++;
          }
        } catch (error) {
          console.error(`❌ Error migrating message ${message.id}:`, error.message);
        }
      }
      console.log(`✅ Migrated ${messages.length} messages`);
    } else {
      console.log('No messages.json file found');
    }

    // 3. Migrate Activation Codes
    console.log('\n3️⃣ Migrating Activation Codes...');
    const codesFile = path.join(DATA_DIR, 'activation_codes.json');
    if (fs.existsSync(codesFile)) {
      const codes = fs.readJsonSync(codesFile);
      console.log(`Found ${codes.length} activation codes to migrate`);
      
      for (const code of codes) {
        try {
          const existingCode = await ActivationCode.findOne({ code: code.code });
          if (!existingCode) {
            await ActivationCode.create(code);
            totalMigrated++;
          }
        } catch (error) {
          console.error(`❌ Error migrating activation code ${code.code}:`, error.message);
        }
      }
      console.log(`✅ Migrated ${codes.length} activation codes`);
    } else {
      console.log('No activation_codes.json file found');
    }

    // 4. Migrate User-specific data (Products, Orders, Store Info)
    console.log('\n4️⃣ Migrating User-specific data...');
    const files = fs.readdirSync(DATA_DIR);
    
    for (const file of files) {
      if (file.startsWith('products_') && file.endsWith('.json')) {
        const userId = file.replace('products_', '').replace('.json', '');
        const products = fs.readJsonSync(path.join(DATA_DIR, file));
        
        console.log(`Migrating ${products.length} products for user ${userId}`);
        for (const product of products) {
          try {
            const existingProduct = await Product.findOne({ id: product.id });
            if (!existingProduct) {
              await Product.create({ ...product, userId });
              totalMigrated++;
            }
          } catch (error) {
            console.error(`❌ Error migrating product ${product.id}:`, error.message);
          }
        }
      }
      
      if (file.startsWith('orders_') && file.endsWith('.json')) {
        const userId = file.replace('orders_', '').replace('.json', '');
        const orders = fs.readJsonSync(path.join(DATA_DIR, file));
        
        console.log(`Migrating ${orders.length} orders for user ${userId}`);
        for (const order of orders) {
          try {
            const existingOrder = await Order.findOne({ id: order.id });
            if (!existingOrder) {
              await Order.create({ ...order, userId });
              totalMigrated++;
            }
          } catch (error) {
            console.error(`❌ Error migrating order ${order.id}:`, error.message);
          }
        }
      }
      
      if (file.startsWith('store_info_') && file.endsWith('.json')) {
        const userId = file.replace('store_info_', '').replace('.json', '');
        const storeInfo = fs.readJsonSync(path.join(DATA_DIR, file));
        
        console.log(`Migrating store info for user ${userId}`);
        try {
          const existingStoreInfo = await StoreInfo.findOne({ userId });
          if (!existingStoreInfo) {
            await StoreInfo.create({ ...storeInfo, userId });
            totalMigrated++;
          }
        } catch (error) {
          console.error(`❌ Error migrating store info for user ${userId}:`, error.message);
        }
      }
    }

    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📊 Total records migrated: ${totalMigrated}`);
    
    // Verify migration
    console.log('\n🔍 Verifying migration...');
    const userCount = await User.countDocuments();
    const productCount = await Product.countDocuments();
    const messageCount = await Message.countDocuments();
    const orderCount = await Order.countDocuments();
    const storeInfoCount = await StoreInfo.countDocuments();
    const codeCount = await ActivationCode.countDocuments();
    
    console.log(`📈 Database statistics:`);
    console.log(`   Users: ${userCount}`);
    console.log(`   Products: ${productCount}`);
    console.log(`   Messages: ${messageCount}`);
    console.log(`   Orders: ${orderCount}`);
    console.log(`   Store Info: ${storeInfoCount}`);
    console.log(`   Activation Codes: ${codeCount}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('\n✅ Migration process completed');
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateData();
}

module.exports = migrateData;
