// Theme Management System
class ThemeManager {
  constructor() {
    this.currentTheme = localStorage.getItem('theme') || 'dark';
    this.themeToggle = document.getElementById('theme-toggle');
    this.themeIcon = document.getElementById('theme-icon');
    
    this.init();
  }

  init() {
    // Set initial theme
    this.setTheme(this.currentTheme);
    
    // Add event listener for theme toggle
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => {
        this.toggleTheme();
      });
    }
  }

  setTheme(theme) {
    this.currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // Update theme icon
    if (this.themeIcon) {
      if (theme === 'dark') {
        this.themeIcon.className = 'fas fa-sun';
        this.themeToggle.title = 'تبديل إلى الوضع النهاري';
      } else {
        this.themeIcon.className = 'fas fa-moon';
        this.themeToggle.title = 'تبديل إلى الوضع الليلي';
      }
    }
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
    
    // Add animation effect
    document.body.style.transition = 'all 0.3s ease';
    setTimeout(() => {
      document.body.style.transition = '';
    }, 300);
  }

  getCurrentTheme() {
    return this.currentTheme;
  }
}

// WhatsApp Integration System
class WhatsAppIntegration {
  constructor() {
    this.phoneNumber = '07518838204';
    this.init();
  }

  init() {
    // Monitor user quota and activation status
    this.checkQuotaStatus();
    
    // Add WhatsApp contact to various sections
    this.addWhatsAppToChat();
    this.addWhatsAppToOrders();
  }

  checkQuotaStatus() {
    // This will be called when user data is loaded
    const checkInterval = setInterval(() => {
      if (window.currentUser) {
        this.updateQuotaDisplay();
        clearInterval(checkInterval);
      }
    }, 1000);
  }

  updateQuotaDisplay() {
    const user = window.currentUser;
    const remainingMessages = document.getElementById('remaining-messages');
    
    if (user && remainingMessages) {
      const remaining = user.freeMessagesRemaining || 0;
      remainingMessages.textContent = remaining;
      
      // Show WhatsApp contact if quota is low or exhausted
      if (remaining <= 5) {
        this.showQuotaWarning(remaining);
      }
    }
  }

  showQuotaWarning(remaining) {
    const chatContainer = document.querySelector('#page-chat .card-body');
    if (chatContainer && !document.querySelector('.quota-exhausted')) {
      const warningDiv = document.createElement('div');
      warningDiv.className = 'quota-exhausted';
      
      if (remaining === 0) {
        warningDiv.innerHTML = `
          <h5><i class="fas fa-exclamation-triangle me-2"></i>تم استنفاد رصيد الرسائل</h5>
          <p>لقد استنفدت رصيد الرسائل المجانية. للحصول على المزيد من الرسائل، يرجى التواصل معنا.</p>
          <div class="whatsapp-contact">
            <h5><i class="fab fa-whatsapp me-2"></i>للحصول على المزيد من الرسائل</h5>
            <p>تواصل معنا عبر واتساب لشراء المزيد من الرسائل أو تفعيل الاشتراك الكامل</p>
            <a href="https://wa.me/${this.phoneNumber}?text=مرحبا، أريد شراء المزيد من الرسائل لنظام الرد التلقائي" 
               class="whatsapp-btn" target="_blank">
              <i class="fab fa-whatsapp"></i>
              تواصل عبر واتساب: ${this.phoneNumber}
            </a>
          </div>
        `;
      } else {
        warningDiv.innerHTML = `
          <h5><i class="fas fa-exclamation-triangle me-2"></i>رصيد الرسائل منخفض</h5>
          <p>لديك ${remaining} رسائل متبقية فقط. تواصل معنا للحصول على المزيد.</p>
          <div class="whatsapp-contact">
            <a href="https://wa.me/${this.phoneNumber}?text=مرحبا، أريد شراء المزيد من الرسائل لنظام الرد التلقائي" 
               class="whatsapp-btn" target="_blank">
              <i class="fab fa-whatsapp"></i>
              تواصل عبر واتساب: ${this.phoneNumber}
            </a>
          </div>
        `;
      }
      
      chatContainer.insertBefore(warningDiv, chatContainer.firstChild);
    }
  }

  addWhatsAppToChat() {
    // Add WhatsApp contact to chat page for support
    const chatPage = document.getElementById('page-chat');
    if (chatPage) {
      const supportDiv = document.createElement('div');
      supportDiv.className = 'card shadow mb-4';
      supportDiv.innerHTML = `
        <div class="card-header">
          <h5 class="mb-0"><i class="fas fa-headset me-2"></i>الدعم الفني</h5>
        </div>
        <div class="card-body">
          <p>هل تحتاج مساعدة في استخدام النظام؟ تواصل معنا عبر واتساب</p>
          <a href="https://wa.me/${this.phoneNumber}?text=مرحبا، أحتاج مساعدة في نظام الرد التلقائي" 
             class="btn btn-success" target="_blank">
            <i class="fab fa-whatsapp me-2"></i>
            الدعم الفني: ${this.phoneNumber}
          </a>
        </div>
      `;
      
      const chatContainer = chatPage.querySelector('.row');
      if (chatContainer) {
        chatContainer.appendChild(supportDiv);
      }
    }
  }

  addWhatsAppToOrders() {
    // Add WhatsApp contact to orders page for order management
    const ordersPage = document.getElementById('page-orders');
    if (ordersPage) {
      const helpDiv = document.createElement('div');
      helpDiv.className = 'alert alert-info';
      helpDiv.innerHTML = `
        <h6><i class="fas fa-info-circle me-2"></i>إدارة الطلبات</h6>
        <p class="mb-2">للمساعدة في إدارة الطلبات أو الاستفسار عن حالة طلب معين، تواصل معنا عبر واتساب</p>
        <a href="https://wa.me/${this.phoneNumber}?text=مرحبا، أحتاج مساعدة في إدارة الطلبات" 
           class="btn btn-success btn-sm" target="_blank">
          <i class="fab fa-whatsapp me-1"></i>
          تواصل معنا: ${this.phoneNumber}
        </a>
      `;
      
      const ordersContainer = ordersPage.querySelector('.container');
      if (ordersContainer) {
        ordersContainer.insertBefore(helpDiv, ordersContainer.children[1]);
      }
    }
  }

  createWhatsAppButton(message, text = null) {
    const encodedMessage = encodeURIComponent(message);
    const buttonText = text || `تواصل عبر واتساب: ${this.phoneNumber}`;
    
    return `
      <a href="https://wa.me/${this.phoneNumber}?text=${encodedMessage}" 
         class="whatsapp-btn" target="_blank">
        <i class="fab fa-whatsapp"></i>
        ${buttonText}
      </a>
    `;
  }
}

// Animation System
class AnimationManager {
  constructor() {
    this.init();
  }

  init() {
    // Add intersection observer for animations
    this.setupScrollAnimations();
    
    // Add hover effects
    this.setupHoverEffects();
  }

  setupScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe cards and important elements
    document.querySelectorAll('.card, .dashboard-card').forEach(el => {
      el.style.opacity = '0';
      el.style.transform = 'translateY(20px)';
      el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      observer.observe(el);
    });
  }

  setupHoverEffects() {
    // Add ripple effect to buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('btn')) {
        this.createRipple(e);
      }
    });
  }

  createRipple(event) {
    const button = event.target;
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    button.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
  }
}

// Initialize all systems when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Initialize theme manager
  window.themeManager = new ThemeManager();
  
  // Initialize WhatsApp integration
  window.whatsAppIntegration = new WhatsAppIntegration();
  
  // Initialize animations
  window.animationManager = new AnimationManager();
  
  console.log('🎨 Theme and UI enhancements loaded successfully!');
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ThemeManager, WhatsAppIntegration, AnimationManager };
}
