const mongoose = require('mongoose');

class Database {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      if (this.isConnected) {
        console.log('Database already connected');
        return this.connection;
      }

      const mongoUri = process.env.MONGODB_URI;
      const dbName = process.env.DB_NAME || 'autoreply_social_media';

      if (!mongoUri) {
        throw new Error('MONGODB_URI environment variable is not set');
      }

      // MongoDB connection options
      const options = {
        maxPoolSize: 10, // Maintain up to 10 socket connections
        serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
        socketTimeoutMS: 45000, // Close sockets after 45 seconds
        dbName: dbName
      };

      console.log('Connecting to MongoDB...');
      this.connection = await mongoose.connect(mongoUri, options);
      this.isConnected = true;

      console.log(`✅ MongoDB connected successfully to database: ${dbName}`);
      console.log(`📍 Connection host: ${this.connection.connection.host}`);
      console.log(`🔌 Connection port: ${this.connection.connection.port}`);

      // Handle connection events
      mongoose.connection.on('error', (error) => {
        console.error('❌ MongoDB connection error:', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        console.log('⚠️ MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        console.log('🔄 MongoDB reconnected');
        this.isConnected = true;
      });

      return this.connection;
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error.message);
      this.isConnected = false;
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.disconnect();
        this.isConnected = false;
        console.log('📴 MongoDB disconnected successfully');
      }
    } catch (error) {
      console.error('❌ Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  async testConnection() {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // Test the connection by running a simple operation
      await mongoose.connection.db.admin().ping();
      console.log('🏓 MongoDB connection test successful');
      return true;
    } catch (error) {
      console.error('❌ MongoDB connection test failed:', error);
      return false;
    }
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  // Graceful shutdown
  async gracefulShutdown() {
    try {
      console.log('🔄 Initiating graceful database shutdown...');
      await this.disconnect();
      console.log('✅ Database shutdown completed');
    } catch (error) {
      console.error('❌ Error during graceful shutdown:', error);
      process.exit(1);
    }
  }
}

// Create a singleton instance
const database = new Database();

// Handle process termination
process.on('SIGINT', async () => {
  await database.gracefulShutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await database.gracefulShutdown();
  process.exit(0);
});

module.exports = database;
