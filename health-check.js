require('dotenv').config();
const database = require('./utils/database');

// Import models for health checks
const User = require('./models/User');
const Product = require('./models/Product');
const Message = require('./models/Message');
const Order = require('./models/Order');
const StoreInfo = require('./models/StoreInfo');
const ActivationCode = require('./models/ActivationCode');

async function performHealthCheck() {
  console.log('🏥 MongoDB Health Check');
  console.log('='.repeat(50));
  console.log(`⏰ Timestamp: ${new Date().toISOString()}\n`);

  const results = {
    database: { status: '❌', details: '' },
    collections: { status: '❌', details: {} },
    indexes: { status: '❌', details: {} },
    performance: { status: '❌', details: {} }
  };

  try {
    // 1. Database Connection Test
    console.log('1️⃣ Testing Database Connection...');
    await database.connect();
    const connectionStatus = database.getConnectionStatus();
    
    if (connectionStatus.isConnected) {
      results.database.status = '✅';
      results.database.details = `Connected to ${connectionStatus.host}:${connectionStatus.port}`;
      console.log(`✅ Database connected: ${connectionStatus.host}:${connectionStatus.port}`);
    } else {
      results.database.details = 'Connection failed';
      console.log('❌ Database connection failed');
    }

    // 2. Collections Health Check
    console.log('\n2️⃣ Checking Collections...');
    const collections = [
      { name: 'Users', model: User },
      { name: 'Products', model: Product },
      { name: 'Messages', model: Message },
      { name: 'Orders', model: Order },
      { name: 'StoreInfo', model: StoreInfo },
      { name: 'ActivationCodes', model: ActivationCode }
    ];

    let allCollectionsHealthy = true;
    for (const collection of collections) {
      try {
        const count = await collection.model.countDocuments();
        results.collections.details[collection.name] = count;
        console.log(`✅ ${collection.name}: ${count} documents`);
      } catch (error) {
        allCollectionsHealthy = false;
        results.collections.details[collection.name] = `Error: ${error.message}`;
        console.log(`❌ ${collection.name}: Error - ${error.message}`);
      }
    }
    results.collections.status = allCollectionsHealthy ? '✅' : '❌';

    // 3. Index Health Check
    console.log('\n3️⃣ Checking Indexes...');
    const collectionNames = ['users', 'products', 'messages', 'orders', 'storeinfos', 'activationcodes'];
    let allIndexesHealthy = true;

    for (const collectionName of collectionNames) {
      try {
        const indexes = await database.connection.connection.db.collection(collectionName).indexes();
        results.indexes.details[collectionName] = indexes.length;
        console.log(`✅ ${collectionName}: ${indexes.length} indexes`);
      } catch (error) {
        allIndexesHealthy = false;
        results.indexes.details[collectionName] = `Error: ${error.message}`;
        console.log(`❌ ${collectionName}: Error - ${error.message}`);
      }
    }
    results.indexes.status = allIndexesHealthy ? '✅' : '❌';

    // 4. Performance Test
    console.log('\n4️⃣ Performance Test...');
    const startTime = Date.now();
    
    try {
      // Test a simple query on each collection
      await User.findOne().lean();
      await Product.findOne().lean();
      await Message.findOne().lean();
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      results.performance.details = `${responseTime}ms`;
      results.performance.status = responseTime < 1000 ? '✅' : '⚠️';
      
      console.log(`${results.performance.status} Query response time: ${responseTime}ms`);
    } catch (error) {
      results.performance.details = `Error: ${error.message}`;
      console.log(`❌ Performance test failed: ${error.message}`);
    }

    // 5. Overall Health Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 Health Check Summary:');
    console.log(`Database Connection: ${results.database.status} ${results.database.details}`);
    console.log(`Collections: ${results.collections.status}`);
    console.log(`Indexes: ${results.indexes.status}`);
    console.log(`Performance: ${results.performance.status} ${results.performance.details}`);

    const overallHealth = [
      results.database.status,
      results.collections.status,
      results.indexes.status,
      results.performance.status
    ].every(status => status === '✅') ? '🟢 HEALTHY' : '🟡 NEEDS ATTENTION';

    console.log(`\n🏥 Overall Status: ${overallHealth}`);

    // 6. Recommendations
    if (overallHealth !== '🟢 HEALTHY') {
      console.log('\n💡 Recommendations:');
      if (results.database.status === '❌') {
        console.log('   - Check MongoDB connection string and network connectivity');
      }
      if (results.collections.status === '❌') {
        console.log('   - Verify collection schemas and data integrity');
      }
      if (results.indexes.status === '❌') {
        console.log('   - Rebuild indexes if necessary');
      }
      if (results.performance.status !== '✅') {
        console.log('   - Consider optimizing queries or adding indexes');
        console.log('   - Check network latency to MongoDB server');
      }
    }

    return results;

  } catch (error) {
    console.error('\n❌ Health check failed:', error);
    return {
      database: { status: '❌', details: error.message },
      collections: { status: '❌', details: {} },
      indexes: { status: '❌', details: {} },
      performance: { status: '❌', details: {} }
    };
  } finally {
    // Disconnect from database
    await database.disconnect();
    console.log('\n🔌 Disconnected from database');
  }
}

// Quick health check (minimal output)
async function quickHealthCheck() {
  try {
    await database.connect();
    const isHealthy = database.getConnectionStatus().isConnected;
    await database.disconnect();
    return isHealthy;
  } catch (error) {
    return false;
  }
}

// Run health check if this script is executed directly
if (require.main === module) {
  performHealthCheck().then((results) => {
    const isHealthy = Object.values(results).every(result => result.status === '✅');
    process.exit(isHealthy ? 0 : 1);
  });
}

module.exports = { performHealthCheck, quickHealthCheck };
