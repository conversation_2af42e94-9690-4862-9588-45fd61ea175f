require('dotenv').config();
const database = require('./utils/database');
const DataStore = require('./utils/dataStoreMongoDB');

async function testDatabaseConnection() {
  console.log('🧪 Starting MongoDB connection test...\n');

  try {
    // Test 1: Database Connection
    console.log('1️⃣ Testing database connection...');
    await database.connect();
    console.log('✅ Database connection successful\n');

    // Test 2: Connection Status
    console.log('2️⃣ Checking connection status...');
    const status = database.getConnectionStatus();
    console.log('📊 Connection Status:', status);
    console.log('✅ Connection status check successful\n');

    // Test 3: Test ping
    console.log('3️⃣ Testing database ping...');
    const pingResult = await database.testConnection();
    console.log('✅ Database ping successful:', pingResult, '\n');

    // Test 4: Test User Operations
    console.log('4️⃣ Testing User CRUD operations...');
    
    // Create a test user
    const testUser = await DataStore.createUser({
      username: 'test_user_' + Date.now(),
      password: 'test_password',
      email: '<EMAIL>',
      name: 'Test User'
    });
    console.log('✅ User created:', testUser.id);

    // Get user by ID
    const retrievedUser = await DataStore.getUserById(testUser.id);
    console.log('✅ User retrieved by ID:', retrievedUser ? 'Success' : 'Failed');

    // Get user by username
    const userByUsername = await DataStore.getUserByUsername(testUser.username);
    console.log('✅ User retrieved by username:', userByUsername ? 'Success' : 'Failed');

    // Update user
    const updatedUser = await DataStore.updateUser(testUser.id, { name: 'Updated Test User' });
    console.log('✅ User updated:', updatedUser ? 'Success' : 'Failed');

    // Test 5: Test Product Operations
    console.log('\n5️⃣ Testing Product CRUD operations...');
    
    // Add a test product
    const testProduct = await DataStore.addProduct(testUser.id, {
      name: 'Test Product',
      price: '100',
      description: 'Test product description'
    });
    console.log('✅ Product created:', testProduct.id);

    // Get products for user
    const products = await DataStore.getProducts(testUser.id);
    console.log('✅ Products retrieved:', products.length > 0 ? 'Success' : 'Failed');

    // Update product
    const updatedProduct = await DataStore.updateProduct(testUser.id, testProduct.id, {
      name: 'Updated Test Product'
    });
    console.log('✅ Product updated:', updatedProduct ? 'Success' : 'Failed');

    // Test 6: Test Message Operations
    console.log('\n6️⃣ Testing Message operations...');
    
    // Add a test message
    const testMessage = await DataStore.addMessage({
      userId: testUser.id,
      platform: 'test',
      content: 'Test message',
      type: 'user'
    });
    console.log('✅ Message created:', testMessage.id);

    // Get messages for user
    const messages = await DataStore.getMessages(testUser.id);
    console.log('✅ Messages retrieved:', messages.length > 0 ? 'Success' : 'Failed');

    // Get conversation history
    const conversation = await DataStore.getConversationHistory(testUser.id, 5);
    console.log('✅ Conversation history retrieved:', conversation.length > 0 ? 'Success' : 'Failed');

    // Test 7: Test Store Info Operations
    console.log('\n7️⃣ Testing Store Info operations...');
    
    // Get store info (should create default if not exists)
    const storeInfo = await DataStore.getStoreInfo(testUser.id);
    console.log('✅ Store info retrieved:', storeInfo ? 'Success' : 'Failed');

    // Update store info
    const updatedStoreInfo = await DataStore.updateStoreInfo(testUser.id, {
      name: 'Test Store',
      address: 'Test Address',
      description: 'Test Description'
    });
    console.log('✅ Store info updated:', updatedStoreInfo ? 'Success' : 'Failed');

    // Test 8: Test Order Operations
    console.log('\n8️⃣ Testing Order operations...');
    
    // Add a test order
    const testOrder = await DataStore.addOrder(testUser.id, {
      productName: 'Test Product Order',
      quantity: 2,
      customerInfo: { name: 'Test Customer' },
      notes: 'Test order notes'
    });
    console.log('✅ Order created:', testOrder.id);

    // Get orders for user
    const orders = await DataStore.getOrders(testUser.id);
    console.log('✅ Orders retrieved:', orders.length > 0 ? 'Success' : 'Failed');

    // Update order status
    const updatedOrder = await DataStore.updateOrderStatus(testUser.id, testOrder.id, 'completed');
    console.log('✅ Order status updated:', updatedOrder ? 'Success' : 'Failed');

    // Test 9: Test Activation Code Operations
    console.log('\n9️⃣ Testing Activation Code operations...');
    
    // Create activation code
    const testCode = await DataStore.createActivationCode({
      type: 'temp',
      description: 'Test activation code'
    });
    console.log('✅ Activation code created:', testCode.code);

    // Get all activation codes
    const codes = await DataStore.getActivationCodes();
    console.log('✅ Activation codes retrieved:', codes.length > 0 ? 'Success' : 'Failed');

    // Use activation code
    const usedCode = await DataStore.useActivationCode(testCode.code, testUser.id);
    console.log('✅ Activation code used:', usedCode ? 'Success' : 'Failed');

    console.log('\n🎉 All database tests completed successfully!');
    console.log('✅ MongoDB integration is working correctly');

  } catch (error) {
    console.error('❌ Database test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Clean up: disconnect from database
    console.log('\n🧹 Cleaning up...');
    await database.disconnect();
    console.log('✅ Database disconnected');
    console.log('🏁 Test completed');
  }
}

// Run the test
testDatabaseConnection();
