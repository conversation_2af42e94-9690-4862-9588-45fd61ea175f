# إعداد Webhook فيسبوك للرد التلقائي

هذا الدليل يشرح كيفية إعداد Webhook فيسبوك لاستقبال الرسائل من صفحات فيسبوك ومعالجتها بشكل آلي باستخدام الذكاء الاصطناعي.

## المتطلبات الأساسية

1. حساب مطور فيسبوك
2. تطبيق فيسبوك تم إنشاؤه في [لوحة تحكم مطوري فيسبوك](https://developers.facebook.com/)
3. صفحة فيسبوك مرتبطة بالتطبيق
4. خادم يمكن الوصول إليه عبر الإنترنت (مع عنوان URL عام)

## خطوات الإعداد

### 1. إعداد التطبيق في لوحة تحكم مطوري فيسبوك

1. قم بتسجيل الدخول إلى [لوحة تحكم مطوري فيسبوك](https://developers.facebook.com/)
2. اختر التطبيق الخاص بك من القائمة
3. انتقل إلى "الإعدادات" > "الأساسية" وتأكد من تسجيل النطاق الخاص بك
4. انتقل إلى "المنتجات" في القائمة الجانبية
5. أضف منتج "Messenger" إذا لم يكن موجودًا بالفعل

### 2. إعداد Webhook في تطبيق فيسبوك

1. في لوحة تحكم مطوري فيسبوك، انتقل إلى "المنتجات" > "Messenger" > "الإعدادات"
2. قم بالتمرير لأسفل إلى قسم "Webhooks"
3. انقر على زر "إضافة Callback URL"
4. أدخل المعلومات التالية:
   - **Callback URL**: `https://your-domain.com/api/webhook` (استبدل your-domain.com بنطاق الخادم الخاص بك)
   - **Verify Token**: `social-media-automation-webhook-token` (أو القيمة التي قمت بتعيينها في ملف .env)
5. انقر على "تحقق وحفظ"

### 3. اشتراك في أحداث الصفحة

1. بعد إعداد Webhook، قم بالتمرير لأسفل إلى قسم "اشتراكات Webhook"
2. انقر على "إضافة اشتراكات"
3. حدد صفحتك من القائمة المنسدلة
4. حدد الأحداث التالية على الأقل:
   - `messages`
   - `messaging_postbacks`
   - `messaging_optins`
   - `message_deliveries`
   - `message_reads`
5. انقر على "حفظ"

### 4. الحصول على رمز وصول الصفحة

1. في قسم "رموز الوصول" في إعدادات Messenger، انقر على "إنشاء رمز وصول جديد"
2. حدد صفحتك من القائمة المنسدلة
3. سيتم إنشاء رمز وصول للصفحة. احتفظ بهذا الرمز آمنًا، فهو ضروري للرد على الرسائل

## اختبار الإعداد

### اختبار التحقق من Webhook

1. تأكد من أن خادمك يعمل وأن نقطة النهاية `/api/webhook` متاحة
2. في لوحة تحكم مطوري فيسبوك، انقر على زر "اختبار" بجوار إعداد Webhook
3. إذا كان كل شيء صحيحًا، ستتلقى رسالة تأكيد

### اختبار استقبال الرسائل

1. افتح صفحة فيسبوك الخاصة بك
2. انتقل إلى المحادثات وأرسل رسالة إلى صفحتك
3. يجب أن يستقبل الخادم الرسالة ويرد عليها تلقائيًا
4. تحقق من سجلات الخادم للتأكد من استلام الرسالة ومعالجتها بشكل صحيح

## استكشاف الأخطاء وإصلاحها

### مشكلة: فشل التحقق من Webhook

- تأكد من أن عنوان URL الخاص بـ Webhook صحيح ويمكن الوصول إليه عبر الإنترنت
- تأكد من أن رمز التحقق في ملف .env يطابق الرمز المدخل في لوحة تحكم مطوري فيسبوك
- تحقق من سجلات الخادم للحصول على رسائل الخطأ

### مشكلة: لا يتم استلام الرسائل

- تأكد من أنك قد اشتركت في أحداث الصفحة الصحيحة
- تحقق من أن رمز وصول الصفحة صالح ولم تنتهي صلاحيته
- تأكد من أن التطبيق في وضع "مباشر" وليس في وضع "تطوير"

### مشكلة: لا يتم إرسال الردود

- تحقق من سجلات الخادم للحصول على أي أخطاء في واجهة برمجة التطبيقات
- تأكد من أن رمز وصول الصفحة صحيح ولديه الأذونات المناسبة
- تحقق من أن الرد يتم إرساله خلال 24 ساعة من استلام رسالة المستخدم (قيود فيسبوك)

## ملاحظات هامة

1. **قيود الرسائل**: يسمح فيسبوك بإرسال رسائل للمستخدمين خلال 24 ساعة فقط من آخر تفاعل منهم
2. **سياسة المراجعة**: قد تحتاج إلى تقديم تطبيقك للمراجعة من قبل فيسبوك قبل أن تتمكن من استخدامه مع جميع المستخدمين
3. **الأمان**: احتفظ برموز الوصول وبيانات الاعتماد الخاصة بك آمنة ولا تشاركها أبدًا

## المراجع

- [توثيق Messenger Platform](https://developers.facebook.com/docs/messenger-platform)
- [إعداد Webhook](https://developers.facebook.com/docs/messenger-platform/webhook)
- [إرسال الرسائل](https://developers.facebook.com/docs/messenger-platform/send-messages)
