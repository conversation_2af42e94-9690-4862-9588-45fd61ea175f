require('dotenv').config();
const axios = require('axios');
const database = require('./utils/database');

const BASE_URL = 'http://localhost:3000';
let authToken = null;
let testUserId = null;

// Test data
const testUser = {
  username: 'mongodb_test_user_' + Date.now(),
  password: 'test123456',
  email: '<EMAIL>',
  name: 'MongoDB Test User'
};

async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    if (authToken) {
      config.headers['x-auth-token'] = authToken;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
}

async function testAPIEndpoints() {
  console.log('🧪 Starting MongoDB Integration API Tests...\n');

  try {
    // Test 1: User Registration
    console.log('1️⃣ Testing User Registration...');
    const registerResult = await makeRequest('POST', '/api/auth/register', testUser);
    
    if (registerResult.success) {
      console.log('✅ User registration successful');
      testUserId = registerResult.data.user?.id;
    } else {
      console.log('❌ User registration failed:', registerResult.error);
      return;
    }

    // Test 2: User Login
    console.log('\n2️⃣ Testing User Login...');
    const loginResult = await makeRequest('POST', '/api/auth/login', {
      username: testUser.username,
      password: testUser.password
    });

    if (loginResult.success && loginResult.data.token) {
      authToken = loginResult.data.token;
      console.log('✅ User login successful');
    } else {
      console.log('❌ User login failed:', loginResult.error);
      return;
    }

    // Test 3: Product Management
    console.log('\n3️⃣ Testing Product Management...');
    
    // Add product
    const productData = {
      name: 'Test MongoDB Product',
      price: '99.99',
      description: 'Test product for MongoDB integration'
    };
    
    const addProductResult = await makeRequest('POST', '/api/products', productData);
    let productId = null;
    
    if (addProductResult.success) {
      productId = addProductResult.data.id;
      console.log('✅ Product creation successful');
    } else {
      console.log('❌ Product creation failed:', addProductResult.error);
    }

    // Get products
    const getProductsResult = await makeRequest('GET', '/api/products');
    if (getProductsResult.success) {
      console.log(`✅ Product retrieval successful (${getProductsResult.data.length} products)`);
    } else {
      console.log('❌ Product retrieval failed:', getProductsResult.error);
    }

    // Update product
    if (productId) {
      const updateProductResult = await makeRequest('PUT', `/api/products/${productId}`, {
        name: 'Updated MongoDB Product'
      });
      
      if (updateProductResult.success) {
        console.log('✅ Product update successful');
      } else {
        console.log('❌ Product update failed:', updateProductResult.error);
      }
    }

    // Test 4: Message/Chat System
    console.log('\n4️⃣ Testing Message/Chat System...');
    
    const chatResult = await makeRequest('POST', '/api/messages/chat', {
      message: 'مرحبا، أريد اختبار النظام',
      platform: 'test'
    });

    if (chatResult.success) {
      console.log('✅ Chat message successful');
    } else {
      console.log('❌ Chat message failed:', chatResult.error);
    }

    // Get message history
    const messagesResult = await makeRequest('GET', '/api/messages');
    if (messagesResult.success) {
      console.log(`✅ Message history retrieval successful (${messagesResult.data.length} messages)`);
    } else {
      console.log('❌ Message history retrieval failed:', messagesResult.error);
    }

    // Test 5: Store Information
    console.log('\n5️⃣ Testing Store Information...');
    
    // Get store info
    const getStoreResult = await makeRequest('GET', '/api/store/info');
    if (getStoreResult.success) {
      console.log('✅ Store info retrieval successful');
    } else {
      console.log('❌ Store info retrieval failed:', getStoreResult.error);
    }

    // Update store info
    const updateStoreResult = await makeRequest('POST', '/api/store/info', {
      name: 'MongoDB Test Store',
      address: 'Test Address',
      description: 'Test store for MongoDB integration'
    });

    if (updateStoreResult.success) {
      console.log('✅ Store info update successful');
    } else {
      console.log('❌ Store info update failed:', updateStoreResult.error);
    }

    // Test 6: Order Management
    console.log('\n6️⃣ Testing Order Management...');
    
    // Add order
    const orderData = {
      productName: 'Test MongoDB Order Product',
      quantity: 2,
      customerInfo: { name: 'Test Customer', phone: '123456789' },
      notes: 'Test order for MongoDB integration'
    };

    const addOrderResult = await makeRequest('POST', '/api/orders', orderData);
    let orderId = null;

    if (addOrderResult.success) {
      orderId = addOrderResult.data.id;
      console.log('✅ Order creation successful');
    } else {
      console.log('❌ Order creation failed:', addOrderResult.error);
    }

    // Get orders
    const getOrdersResult = await makeRequest('GET', '/api/orders');
    if (getOrdersResult.success) {
      console.log(`✅ Order retrieval successful (${getOrdersResult.data.length} orders)`);
    } else {
      console.log('❌ Order retrieval failed:', getOrdersResult.error);
    }

    // Update order status
    if (orderId) {
      const updateOrderResult = await makeRequest('PUT', `/api/orders/${orderId}/status`, {
        status: 'completed'
      });

      if (updateOrderResult.success) {
        console.log('✅ Order status update successful');
      } else {
        console.log('❌ Order status update failed:', updateOrderResult.error);
      }
    }

    console.log('\n🎉 All API tests completed successfully!');
    console.log('✅ MongoDB integration is working correctly with all endpoints');

  } catch (error) {
    console.error('❌ API test failed:', error);
  }
}

async function verifyDatabaseIntegrity() {
  console.log('\n🔍 Verifying Database Integrity...\n');

  try {
    await database.connect();

    // Import models for direct database verification
    const User = require('./models/User');
    const Product = require('./models/Product');
    const Message = require('./models/Message');
    const Order = require('./models/Order');
    const StoreInfo = require('./models/StoreInfo');
    const ActivationCode = require('./models/ActivationCode');

    // Check data consistency
    console.log('📊 Database Statistics:');
    console.log(`   Users: ${await User.countDocuments()}`);
    console.log(`   Products: ${await Product.countDocuments()}`);
    console.log(`   Messages: ${await Message.countDocuments()}`);
    console.log(`   Orders: ${await Order.countDocuments()}`);
    console.log(`   Store Info: ${await StoreInfo.countDocuments()}`);
    console.log(`   Activation Codes: ${await ActivationCode.countDocuments()}`);

    // Verify indexes
    console.log('\n🔍 Verifying Indexes...');
    const collections = ['users', 'products', 'messages', 'orders', 'storeinfos', 'activationcodes'];
    
    for (const collectionName of collections) {
      try {
        const indexes = await database.connection.connection.db.collection(collectionName).indexes();
        console.log(`✅ ${collectionName}: ${indexes.length} indexes`);
      } catch (error) {
        console.log(`⚠️ ${collectionName}: Collection not found or no indexes`);
      }
    }

    console.log('\n✅ Database integrity verification completed');

  } catch (error) {
    console.error('❌ Database integrity check failed:', error);
  } finally {
    await database.disconnect();
  }
}

async function runFullVerification() {
  console.log('🚀 Starting Full MongoDB Integration Verification\n');
  console.log('=' .repeat(60));

  // First verify database integrity
  await verifyDatabaseIntegrity();

  console.log('\n' + '=' .repeat(60));

  // Then test API endpoints
  await testAPIEndpoints();

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Full verification completed!');
  console.log('✅ MongoDB integration is fully functional');
}

// Run verification if this script is executed directly
if (require.main === module) {
  runFullVerification();
}

module.exports = { testAPIEndpoints, verifyDatabaseIntegrity, runFullVerification };
