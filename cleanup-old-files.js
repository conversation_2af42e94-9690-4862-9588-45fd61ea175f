const fs = require('fs-extra');
const path = require('path');

const DATA_DIR = path.join(__dirname, 'data');
const BACKUP_DIR = path.join(__dirname, 'data_backup_' + new Date().toISOString().replace(/[:.]/g, '-'));

async function cleanupOldFiles() {
  console.log('🧹 Starting cleanup of old JSON files...\n');

  try {
    // Check if data directory exists
    if (!fs.existsSync(DATA_DIR)) {
      console.log('⚠️ No data directory found. Nothing to clean up.');
      return;
    }

    // Create backup directory
    console.log('📦 Creating backup of old files...');
    await fs.ensureDir(BACKUP_DIR);

    // Get list of files in data directory
    const files = await fs.readdir(DATA_DIR);
    const jsonFiles = files.filter(file => file.endsWith('.json'));

    if (jsonFiles.length === 0) {
      console.log('⚠️ No JSON files found to clean up.');
      return;
    }

    console.log(`Found ${jsonFiles.length} JSON files to backup and remove:`);
    jsonFiles.forEach(file => console.log(`  - ${file}`));

    // Copy files to backup directory
    console.log('\n📋 Copying files to backup...');
    for (const file of jsonFiles) {
      const sourcePath = path.join(DATA_DIR, file);
      const backupPath = path.join(BACKUP_DIR, file);
      
      await fs.copy(sourcePath, backupPath);
      console.log(`✅ Backed up: ${file}`);
    }

    // Ask for confirmation (in a real scenario, you might want to add interactive confirmation)
    console.log('\n⚠️  WARNING: This will permanently delete the old JSON files!');
    console.log('📦 Backup created at:', BACKUP_DIR);
    console.log('🔄 MongoDB migration should be tested and verified before proceeding.');
    
    // For safety, we'll just show what would be deleted instead of actually deleting
    console.log('\n🛡️  SAFETY MODE: Files will NOT be deleted automatically.');
    console.log('To actually delete the files, uncomment the deletion code in this script.');
    console.log('\nFiles that would be deleted:');
    
    for (const file of jsonFiles) {
      console.log(`  - ${path.join(DATA_DIR, file)}`);
    }

    // Uncomment the following code block to actually delete the files
    /*
    console.log('\n🗑️  Deleting old JSON files...');
    for (const file of jsonFiles) {
      const filePath = path.join(DATA_DIR, file);
      await fs.remove(filePath);
      console.log(`🗑️  Deleted: ${file}`);
    }

    // Remove data directory if it's empty
    const remainingFiles = await fs.readdir(DATA_DIR);
    if (remainingFiles.length === 0) {
      await fs.remove(DATA_DIR);
      console.log('🗑️  Removed empty data directory');
    }
    */

    console.log('\n✅ Cleanup process completed!');
    console.log('📦 Backup location:', BACKUP_DIR);
    console.log('🔒 Original files are safe until you manually delete them.');

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Instructions for manual cleanup
function showManualCleanupInstructions() {
  console.log('\n📋 Manual Cleanup Instructions:');
  console.log('================================');
  console.log('');
  console.log('1. Verify MongoDB integration is working correctly:');
  console.log('   npm start');
  console.log('   node verify-mongodb-integration.js');
  console.log('');
  console.log('2. Test your application thoroughly');
  console.log('');
  console.log('3. When you\'re confident everything works, you can manually delete:');
  console.log('   - data/users.json');
  console.log('   - data/messages.json');
  console.log('   - data/activation_codes.json');
  console.log('   - data/products_*.json');
  console.log('   - data/orders_*.json');
  console.log('   - data/store_info_*.json');
  console.log('   - data/ directory (if empty)');
  console.log('');
  console.log('4. Keep the backup directory for safety:');
  console.log(`   ${BACKUP_DIR}`);
  console.log('');
  console.log('⚠️  IMPORTANT: Only delete the old files after thorough testing!');
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanupOldFiles().then(() => {
    showManualCleanupInstructions();
  });
}

module.exports = { cleanupOldFiles, showManualCleanupInstructions };
