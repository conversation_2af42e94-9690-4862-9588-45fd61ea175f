/* CSS Variables for Theme Management */
:root {
  /* Dark Theme Colors */
  --bg-primary: #000000;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2d2d2d;
  --bg-card: #1e1e1e;
  --bg-modal: #252525;

  /* Golden Gradient Colors */
  --gold-light: #FFD700;
  --gold-medium: #FFC107;
  --gold-dark: #B8860B;
  --gold-darker: #996515;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-muted: #b0b0b0;
  --text-gold: var(--gold-light);

  /* Border Colors */
  --border-primary: #404040;
  --border-secondary: #555555;
  --border-gold: var(--gold-medium);

  /* WhatsApp Colors */
  --whatsapp-green: #25D366;
  --whatsapp-green-dark: #1da851;

  /* Status Colors */
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
}

/* Light Theme Variables */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-card: #ffffff;
  --bg-modal: #ffffff;

  --text-primary: #212529;
  --text-secondary: #495057;
  --text-muted: #6c757d;
  --text-gold: var(--gold-dark);

  --border-primary: #dee2e6;
  --border-secondary: #ced4da;
}

/* General Styles */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.page {
  display: none;
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Theme Toggle Button */
.theme-toggle {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1050;
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bg-primary);
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Card Styles */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.card-header {
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  color: var(--bg-primary);
  border-bottom: none;
  font-weight: 600;
  padding: 1rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
  color: var(--text-primary);
}

/* Button Styles */
.btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  text-transform: none;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  color: var(--bg-primary);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(45deg, var(--gold-medium), var(--gold-darker));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
  color: var(--bg-primary);
}

.btn-outline-primary {
  border: 2px solid var(--gold-medium);
  color: var(--gold-light);
  background: transparent;
}

.btn-outline-primary:hover {
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  color: var(--bg-primary);
  border-color: var(--gold-light);
}

.btn-light {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
}

.btn-light:hover {
  background: var(--bg-card);
  color: var(--text-gold);
}

.btn-outline-light {
  border: 2px solid var(--border-secondary);
  color: var(--text-secondary);
  background: transparent;
}

.btn-outline-light:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Navbar Styles */
.navbar {
  background: linear-gradient(45deg, var(--bg-primary), var(--bg-secondary)) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  border-bottom: 2px solid var(--gold-medium);
  padding: 1rem 0;
}

.navbar-brand {
  font-weight: bold;
  color: var(--text-gold) !important;
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-brand:hover {
  color: var(--gold-light) !important;
  transform: scale(1.05);
}

.navbar-nav .nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  border-radius: 6px;
  transition: all 0.3s ease;
  margin: 0 0.2rem;
}

.navbar-nav .nav-link:hover {
  color: var(--text-gold) !important;
  background: rgba(255, 215, 0, 0.1);
  transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
  color: var(--gold-light) !important;
  background: rgba(255, 215, 0, 0.2);
}

.navbar-toggler {
  border: 2px solid var(--gold-medium);
  padding: 0.5rem;
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%23FFD700' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Chat Styles */
.chat-container {
  height: 400px;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
  border-radius: 15px;
  border: 1px solid var(--border-primary);
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.chat-container::-webkit-scrollbar {
  width: 8px;
}

.chat-container::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.chat-container::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  border-radius: 4px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--gold-medium), var(--gold-darker));
}

.user-message, .bot-message, .system-message {
  max-width: 80%;
  margin-bottom: 15px;
  position: relative;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-content {
  padding: 12px 15px;
  border-radius: 15px;
  line-height: 1.5;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.user-message {
  align-self: flex-end;
}

.user-message .message-content {
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  color: var(--bg-primary);
  border-bottom-right-radius: 5px;
  font-weight: 500;
}

.bot-message {
  align-self: flex-start;
}

.bot-message .message-content {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-bottom-left-radius: 5px;
}

.system-message {
  align-self: center;
  max-width: 90%;
}

.system-message .message-content {
  background: var(--bg-tertiary);
  color: var(--text-muted);
  border-radius: 10px;
  font-style: italic;
  text-align: center;
  border: 1px solid var(--border-secondary);
}

.message-time {
  font-size: 0.7rem;
  color: var(--text-muted);
  margin-top: 4px;
  display: block;
  text-align: right;
}

.user-message .message-time {
  color: rgba(0, 0, 0, 0.7);
}

.bot-message .message-time {
  text-align: left;
  color: var(--text-muted);
}

.message-indicator {
  width: 10px;
  height: 10px;
  position: absolute;
  bottom: 5px;
}

.user-message .message-indicator {
  right: -5px;
  border-right: 10px solid var(--gold-medium);
  border-bottom: 10px solid transparent;
}

.bot-message .message-indicator {
  left: -5px;
  border-left: 10px solid var(--bg-card);
  border-bottom: 10px solid transparent;
}

/* WhatsApp Contact Integration */
.whatsapp-contact {
  background: linear-gradient(45deg, var(--whatsapp-green), var(--whatsapp-green-dark));
  color: white;
  padding: 1rem;
  border-radius: 12px;
  margin: 1rem 0;
  text-align: center;
  box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3); }
  50% { box-shadow: 0 6px 20px rgba(37, 211, 102, 0.5); }
  100% { box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3); }
}

.whatsapp-contact h5 {
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.whatsapp-contact p {
  margin-bottom: 1rem;
  opacity: 0.9;
}

.whatsapp-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.whatsapp-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: translateY(-2px);
  text-decoration: none;
}

.whatsapp-btn i {
  font-size: 1.2rem;
}

/* Quota Exhausted Alert */
.quota-exhausted {
  background: linear-gradient(45deg, var(--danger-color), #c82333);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin: 1rem 0;
  text-align: center;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.quota-exhausted h5 {
  margin-bottom: 1rem;
  font-weight: 600;
}

.quota-exhausted .whatsapp-contact {
  margin-top: 1rem;
  background: var(--whatsapp-green);
}

/* Form Styles */
.form-control {
  background: var(--bg-card);
  border: 2px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  background: var(--bg-card);
  border-color: var(--gold-medium);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-text {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.input-group .form-control {
  border-right: none;
}

.input-group .btn {
  border-left: none;
  border-color: var(--border-primary);
}

/* Table Styles */
.table {
  color: var(--text-primary);
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  color: var(--text-gold);
  border-bottom: 2px solid var(--border-gold);
  background: var(--bg-tertiary);
  padding: 1rem 0.75rem;
}

.table td {
  border-bottom: 1px solid var(--border-primary);
  padding: 0.75rem;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: var(--bg-card);
  border-radius: 15px;
  margin-bottom: 10px;
  align-self: flex-start;
  border: 1px solid var(--border-primary);
}

.typing-indicator span {
  margin-right: 2px;
  color: var(--text-muted);
}

.typing-indicator .dot {
  animation: typingAnimation 1.4s infinite;
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--gold-medium);
  margin: 0 2px;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0% { opacity: 0.3; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
  100% { opacity: 0.3; transform: scale(0.8); }
}

/* Dashboard Styles */
.dashboard-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.dashboard-card i {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-card h5 {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.dashboard-card h3 {
  color: var(--text-gold);
  font-weight: 700;
  font-size: 2rem;
}

/* Action Buttons */
.action-buttons .btn {
  padding: 6px 12px;
  font-size: 0.8rem;
  margin: 0 2px;
  border-radius: 6px;
}

.btn-success {
  background: linear-gradient(45deg, var(--success-color), #1e7e34);
  border: none;
  color: white;
}

.btn-success:hover {
  background: linear-gradient(45deg, #1e7e34, var(--success-color));
  transform: translateY(-1px);
}

.btn-warning {
  background: linear-gradient(45deg, var(--warning-color), #e0a800);
  border: none;
  color: var(--bg-primary);
}

.btn-warning:hover {
  background: linear-gradient(45deg, #e0a800, var(--warning-color));
  transform: translateY(-1px);
}

.btn-danger {
  background: linear-gradient(45deg, var(--danger-color), #c82333);
  border: none;
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(45deg, #c82333, var(--danger-color));
  transform: translateY(-1px);
}

.btn-info {
  background: linear-gradient(45deg, var(--info-color), #138496);
  border: none;
  color: white;
}

.btn-info:hover {
  background: linear-gradient(45deg, #138496, var(--info-color));
  transform: translateY(-1px);
}

/* Modal Styles */
.modal-content {
  background: var(--bg-modal);
  border: 1px solid var(--border-primary);
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  color: var(--bg-primary);
  border-bottom: none;
  border-radius: 15px 15px 0 0;
  padding: 1.5rem;
}

.modal-title {
  font-weight: 600;
}

.modal-body {
  padding: 1.5rem;
  color: var(--text-primary);
}

.modal-footer {
  border-top: 1px solid var(--border-primary);
  padding: 1rem 1.5rem;
}

.btn-close {
  background: none;
  border: none;
  color: var(--bg-primary);
  opacity: 0.8;
  font-size: 1.2rem;
}

.btn-close:hover {
  opacity: 1;
}

/* Alert Styles */
.alert {
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border-left: 4px solid;
}

.alert-success {
  background: linear-gradient(45deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
  color: var(--success-color);
  border-left-color: var(--success-color);
}

.alert-warning {
  background: linear-gradient(45deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
  color: var(--warning-color);
  border-left-color: var(--warning-color);
}

.alert-danger {
  background: linear-gradient(45deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
  color: var(--danger-color);
  border-left-color: var(--danger-color);
}

.alert-info {
  background: linear-gradient(45deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
  color: var(--info-color);
  border-left-color: var(--info-color);
}

/* Loading Spinner */
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 215, 0, 0.2);
  border-radius: 50%;
  border-left-color: var(--gold-medium);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Admin Panel Styles */
.admin-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 15px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
}

.admin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.admin-card .card-body {
  padding: 2rem;
  text-align: center;
}

.admin-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Success/Error Pages */
.success-container, .error-container {
  text-align: center;
  padding: 50px 20px;
  background: var(--bg-card);
  border-radius: 15px;
  margin: 2rem auto;
  max-width: 600px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.success-icon {
  font-size: 5rem;
  color: var(--success-color);
  margin-bottom: 20px;
  animation: bounce 1s ease-in-out;
}

.error-icon {
  font-size: 5rem;
  color: var(--danger-color);
  margin-bottom: 20px;
  animation: shake 0.5s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .user-message, .bot-message {
    max-width: 90%;
  }

  .theme-toggle {
    top: 10px;
    left: 10px;
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .dashboard-card {
    padding: 1.5rem;
  }

  .dashboard-card i {
    font-size: 2.5rem;
  }

  .dashboard-card h3 {
    font-size: 1.5rem;
  }

  .card-body {
    padding: 1rem;
  }

  .navbar-brand {
    font-size: 1.2rem;
  }

  .whatsapp-contact {
    padding: 0.75rem;
  }

  .whatsapp-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 10px;
  }

  .card {
    margin-bottom: 1rem;
  }

  .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .action-buttons .btn {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
}

/* RTL Specific Adjustments */
.dropdown-menu {
  text-align: right;
  background: var(--bg-modal);
  border: 1px solid var(--border-primary);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  color: var(--text-primary);
}

.dropdown-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-gold);
}

/* Features List Styles */
.features-list {
  background: var(--bg-tertiary);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.feature-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  color: var(--text-primary);
}

.feature-item i {
  color: var(--success-color);
  margin-left: 10px;
}

/* Connected Accounts Styles */
.connected-accounts-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.connected-accounts-list li {
  padding: 15px;
  margin-bottom: 10px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.connected-accounts-list li .account-icon {
  width: 40px;
  height: 40px;
  margin-left: 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.connected-accounts-list li .fb-icon {
  background-color: #3b5998;
}

.connected-accounts-list li .ig-icon {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.account-details {
  flex: 1;
  color: var(--text-primary);
}

.account-name {
  font-weight: 500;
  margin-bottom: 2px;
  color: var(--text-primary);
}

.account-type {
  font-size: 12px;
  color: var(--text-muted);
}

.account-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  background-color: var(--success-color);
  color: white;
  display: inline-block;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--gold-medium), var(--gold-darker));
}

/* Ripple Effect */
.btn {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Enhanced Focus States */
.form-control:focus,
.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.25);
}

/* Loading States */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Enhanced Tooltips */
[title] {
  position: relative;
}

[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-modal);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-primary);
}

/* Enhanced Animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Page Transition Animations */
.page {
  animation: slideInFromBottom 0.5s ease-out;
}

.navbar {
  animation: slideInFromTop 0.5s ease-out;
}

.dashboard-card:nth-child(1) {
  animation: slideInFromLeft 0.6s ease-out;
}

.dashboard-card:nth-child(2) {
  animation: slideInFromBottom 0.7s ease-out;
}

.dashboard-card:nth-child(3) {
  animation: slideInFromRight 0.8s ease-out;
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

.status-online {
  background: var(--success-color);
  box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
  animation: pulse-green 2s infinite;
}

.status-offline {
  background: var(--danger-color);
}

.status-pending {
  background: var(--warning-color);
  animation: pulse-yellow 2s infinite;
}

@keyframes pulse-green {
  0% { box-shadow: 0 0 8px rgba(40, 167, 69, 0.5); }
  50% { box-shadow: 0 0 15px rgba(40, 167, 69, 0.8); }
  100% { box-shadow: 0 0 8px rgba(40, 167, 69, 0.5); }
}

@keyframes pulse-yellow {
  0% { box-shadow: 0 0 8px rgba(255, 193, 7, 0.5); }
  50% { box-shadow: 0 0 15px rgba(255, 193, 7, 0.8); }
  100% { box-shadow: 0 0 8px rgba(255, 193, 7, 0.5); }
}

/* Print Styles */
@media print {
  .theme-toggle,
  .navbar,
  .btn,
  .whatsapp-contact {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }
}
